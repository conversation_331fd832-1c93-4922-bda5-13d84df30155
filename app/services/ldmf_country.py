import logging

import pycountry
from pycountry.db import Country

from repositories.ldmf_countries import LDMFCountriesRepository
from schemas import CountryData


__all__ = ['LDMFCountryService']

logger = logging.getLogger(__name__)


class LDMFCountryService:
    """Service for LDMF country operations."""

    _cached_ldmf_countries: dict[str, CountryData] = {}

    def __init__(self, ldmf_countries_repository: LDMFCountriesRepository):
        self.ldmf_countries_repository = ldmf_countries_repository

    async def _get_cached_ldmf_countries(self, token: str) -> dict[str, CountryData]:
        """
        Get cached LDMF countries.

        Returns:
            Dictionary of LDMF countries.

        Raises:
            Exception: If an error occurs while getting cached LDMF countries.
        """
        try:
            logger.info('Getting cached LDMF countries')

            if not self._cached_ldmf_countries:
                ldmf_countries = await self.ldmf_countries_repository.list(token)
                type(self)._cached_ldmf_countries = {ldmf_country.name: ldmf_country for ldmf_country in ldmf_countries}
                logger.info('LDMF countries cached')

            return self._cached_ldmf_countries
        except Exception as e:
            logger.error('Error listing industries: %s', e)
            raise

    async def verify_ldmf_country(self, ldmf_country: str, token: str) -> str | None:
        if ldmf_country in (await self._get_cached_ldmf_countries(token)):
            return ldmf_country

        country = self._perform_lookup(ldmf_country)
        if country:
            return country

        country = self._perform_search_fuzzy(ldmf_country)
        if country:
            return country

        return None

    @staticmethod
    def _perform_lookup(ldmf_country: str) -> str | None:
        try:
            country: Country = pycountry.countries.lookup(ldmf_country)  # type: ignore
            return country.name
        except LookupError:
            pass
        return None

    @staticmethod
    def _perform_search_fuzzy(ldmf_country: str) -> str | None:
        try:
            countries = pycountry.countries.search_fuzzy(ldmf_country)
            country: Country = countries[0]  # type: ignore
            return country.name
        except LookupError:
            pass
        return None
